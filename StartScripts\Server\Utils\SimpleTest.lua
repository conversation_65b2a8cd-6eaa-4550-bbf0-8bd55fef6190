--!strict
-- 简单测试脚本
-- 用于测试职业选择和物品数据传输功能

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local ProfessionManager = require(ReplicatedStorage.Scripts.Server.Manager.ProfessionManager)
local TeleportManager = require(ReplicatedStorage.Scripts.Server.Manager.TeleportManager)
local OccupationDataParser = require(script.Parent.OccupationDataParser)
local OccupationConfig = require(script.Parent.Parent.Parent.Config.OccupationConfig)

local SimpleTest = {}

-- 为玩家设置测试职业
function SimpleTest.setTestProfession(player, professionId)
    local userId = player.UserId
    local profession = "测试职业" .. professionId
    
    -- 直接设置职业数据
    ProfessionManager.playerProfession[userId] = profession
    ProfessionManager.playerProfessionState[userId] = professionId
    
    -- 获取职业对应的物品数据
    local playerItems = OccupationDataParser.getItemsByOccupationId(professionId, OccupationConfig)
    
    -- 存储玩家的物品数据
    if not ProfessionManager.playerItems then
        ProfessionManager.playerItems = {}
    end
    ProfessionManager.playerItems[userId] = {
        items = playerItems,
        playerName = player.Name,
        profession = profession,
        professionId = professionId
    }
    
    print("SimpleTest: 为玩家 " .. player.Name .. " 设置职业 " .. profession .. "，物品数量: " .. #playerItems)
    
    -- 打印物品详情
    for i, item in ipairs(playerItems) do
        print(string.format("  物品[%d]: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
    end
end

-- 测试传送数据生成
function SimpleTest.testTeleportData()
    print("=== 测试传送数据生成 ===")
    
    local players = Players:GetPlayers()
    if #players == 0 then
        warn("没有在线玩家")
        return
    end
    
    -- 为玩家设置不同职业
    for i, player in ipairs(players) do
        local professionId = i == 1 and 4001 or 4002  -- 第一个玩家4001，其他4002
        SimpleTest.setTestProfession(player, professionId)
    end
    
    -- 模拟生成传送数据
    local playersData = {}
    for _, player in ipairs(players) do
        local userId = player.UserId
        local playerItems = {}
        local profession = nil
        local professionId = nil
        
        if ProfessionManager.playerItems and ProfessionManager.playerItems[userId] then
            local playerData = ProfessionManager.playerItems[userId]
            playerItems = playerData.items
            profession = playerData.profession
            professionId = playerData.professionId
        end
        
        playersData[player.Name] = {
            items = playerItems,
            profession = profession,
            professionId = professionId,
            userId = userId
        }
    end
    
    -- 打印生成的数据
    print("生成的传送数据:")
    for playerName, data in pairs(playersData) do
        print("玩家: " .. playerName)
        print("  职业: " .. (data.profession or "无"))
        print("  职业ID: " .. (data.professionId or "无"))
        print("  用户ID: " .. data.userId)
        print("  物品数量: " .. #data.items)
        for i, item in ipairs(data.items) do
            print(string.format("    物品[%d]: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
        end
        print("")
    end
    
    print("=== 测试完成 ===")
end

-- 清理测试数据
function SimpleTest.cleanup()
    print("清理测试数据...")
    
    for _, player in ipairs(Players:GetPlayers()) do
        local userId = player.UserId
        ProfessionManager.playerProfession[userId] = nil
        ProfessionManager.playerProfessionState[userId] = nil
        if ProfessionManager.playerItems then
            ProfessionManager.playerItems[userId] = nil
        end
        print("已清理玩家 " .. player.Name .. " 的数据")
    end
end

-- 设置测试命令
function SimpleTest.setupCommands()
    print("设置简单测试命令...")
    
    for _, player in ipairs(Players:GetPlayers()) do
        SimpleTest.setupPlayerCommands(player)
    end
    
    Players.PlayerAdded:Connect(function(player)
        SimpleTest.setupPlayerCommands(player)
    end)
end

function SimpleTest.setupPlayerCommands(player)
    player.Chatted:Connect(function(message)
        if message == "/test_simple" then
            SimpleTest.testTeleportData()
        elseif message == "/cleanup_simple" then
            SimpleTest.cleanup()
        elseif message == "/set_job1" then
            SimpleTest.setTestProfession(player, 4001)
        elseif message == "/set_job2" then
            SimpleTest.setTestProfession(player, 4002)
        elseif message == "/help_simple" then
            print("简单测试命令:")
            print("  /test_simple - 测试传送数据生成")
            print("  /cleanup_simple - 清理测试数据")
            print("  /set_job1 - 设置职业4001")
            print("  /set_job2 - 设置职业4002")
        end
    end)
end

return SimpleTest
